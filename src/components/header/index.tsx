import React, { useEffect, useState } from 'react'
import { useRouter } from 'next/router'
import Link from 'next/link'
import Image from 'next/image'
import { useMenu } from '@/contexts'
import IconButton from './icon-button'
import IconMenu from './icon-menu'
import Menu from './menu'
import IconNotice from './icon-notice'
import IconUser from './icon-user'
import Workbench from './workbench'
import Search, { SearchIcon } from './search'
export * from './search'
export { Search, SearchIcon }
import Cookies from 'js-cookie'
import userApi from '@/services/api/user'
import { clearTopLevelCookies } from '@/utils/cookie'

export default function Header() {
  const router = useRouter()
  const { menus } = useMenu()

  const items = [
    {
      title: 'Developer Portal',
      url: '/',
    },
    ...menus,
  ]

  const handleSearch = () => {
    router.push(`/search?keyword=`)
  }
  const [userInfo, setUserInfo] = useState(null)
  const getUserInfoWithCookie = async () => {
    const token = Cookies.get('token_new')
    if (token) {
      const res = await userApi.getUserInfo(token)
      if (res.code === 200) {
        setUserInfo(res.data)
      }
    }
  }

  const [hasLogin, setHasLogin] = useState(false)
  useEffect(() => {
    setHasLogin(!!userInfo)
  }, [userInfo])

  const handleLogout = async () => {
    const token = Cookies.get('token_new')
    console.log('删除前的token:', token)
    if (token) {
      const res = await userApi.logout(token)
      if (res.code === 200) {
        clearTopLevelCookies(['token_new'])
        setUserInfo(null)
        console.log('删除后的token:', Cookies.get('token_new'))
        window.location.reload()
      }
    }
  }

  useEffect(() => {
    getUserInfoWithCookie()
  }, [])
  return (
    <>
      <div className="relative z-10 border-b border-solid border-[#E5E5E5]">
        <div className="relative z-10 mx-auto flex h-16 max-w-[1920px] items-center px-6 text-textColorBlack max-xl:h-12 max-xl:px-2">
          <IconMenu className="mr-3 shrink-0 xl:hidden" />

          <Link
            href="/"
            aria-label="Home"
            className="mr-3 w-[76px] shrink-0 max-xl:w-[58px]"
          >
            <Image src="/logo.svg" alt="Fs logo" width={76} height={36} />
          </Link>

          <Menu className="max-xl:hidden" items={items} />

          <div className="ml-auto flex items-center space-x-3">
            <div className="w-[240px] overflow-hidden rounded bg-[#FAFAFB] max-xl:hidden 2xl:w-[380px]">
              <Search />
            </div>
            <IconButton className="xl:hidden" onClick={handleSearch}>
              <i className="iconfont iconsearch-daohangsousuo text-[24px]"></i>
            </IconButton>
            {hasLogin ? <IconNotice hasLogin={hasLogin} /> : null}
            <IconUser userInfo={userInfo} logout={handleLogout} />
            <Workbench className="max-sm:hidden" />
          </div>
        </div>
      </div>
    </>
  )
}
